<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents an allocation.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class Allocation
{
    // --- Attributes ---
    private ?string $id         = null; // Stores the 'desordenado' ID
    private ?string $nombre     = null;
    private ?float  $porcentaje = null;
    private ?int    $estado     = null; // 0 = Inactivo, 1 = Activo

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id         = null;
        $this->nombre     = null;
        $this->porcentaje = null;
        $this->estado     = null; // Default state might be considered active (1) upon creation, adjust if needed
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of Allocation.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing Allocation ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }
            // We store the 'desordenado' string ID internally.
            $objeto->nombre     = $data['nombre'] ?? null;
            $objeto->porcentaje = isset($data['porcentaje']) ? (float)$data['porcentaje'] : null;
            $objeto->estado     = isset($data['estado']) ? (int)$data['estado'] : null;

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing Allocation from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing Allocation: " . $e->getMessage());
        }
    }

    /**
     * Retrieves an Allocation object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the Allocation to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null An Allocation object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM allocations
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting Allocation (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching Allocation: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting Allocation (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching Allocation: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active Allocation objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of Allocation objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM allocations
            WHERE estado = :estado
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            // Assuming 1 means active
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing Allocation during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Decide whether to skip this item or re-throw
                        // For now, skip and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting Allocation list: " . $e->getMessage());
            throw new Exception("Database error fetching Allocation list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting Allocation list: " . $e->getMessage());
            throw new Exception("Error fetching Allocation list: " . $e->getMessage());
        }
    }

    /**
     * Calculates the sum of 'porcentaje' for an array of Allocation objects.
     *
     * @param Allocation[] $allocations An array of Allocation objects.
     * @return float The total sum of 'porcentaje'.
     */
    public static function getSumPorcentaje(array $allocations): float
    {
        $porcentajeTotal = 0.0;
        foreach ($allocations as $allocation) {
            // Use getter to access private property
            if ($allocation instanceof self && $allocation->getPorcentaje() !== null) {
                $porcentajeTotal += $allocation->getPorcentaje();
            }
        }
        return $porcentajeTotal;
    }

    /**
     * Saves (inserts or updates) the current Allocation instance to the database.
     * Sets the 'estado' to 1 (active) implicitly during insert/update if not set.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("Allocation::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("Allocation::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving Allocation (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving Allocation: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving Allocation: " . $e->getMessage());
            throw new Exception("Error saving Allocation: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current Allocation instance into the database. (Private method)
     * Sets estado to 1 (active) by default on insert.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        // Assume new allocations are active by default
        $estadoParaGuardar = $this->getEstado() ?? 1;

        $query = <<<SQL
        INSERT INTO allocations (
            nombre,
            porcentaje,
            estado
        ) VALUES (
            :nombre,
            :porcentaje,
            :estado
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':porcentaje', $this->getPorcentaje(), PDO::PARAM_STR); // PDO often handles float as string
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
                // Also update the object properties with values actually inserted
                $this->setEstado($estadoParaGuardar);
            } else {
                 error_log("Failed to retrieve lastInsertId after Allocation insert.");
                 return false;
            }
        } else {
            error_log("Failed to insert Allocation: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current Allocation instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update Allocation without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the Allocation ID for update: " . $this->getId());
         }

        // Use current estado if set, otherwise default to 1 (active)
        $estadoParaGuardar = $this->getEstado() ?? 1;

        $query = <<<SQL
        UPDATE allocations SET
             nombre = :nombre
            ,porcentaje = :porcentaje
            ,estado = :estado
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':porcentaje', $this->getPorcentaje(), PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update Allocation (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        } else {
             // Ensure object's estado reflects the saved state
             $this->setEstado($estadoParaGuardar);
        }

        return $success;
    }

    /**
     * Deletes (soft deletes) an Allocation record by setting its estado to 0.
     *
     * @param string $id       The 'desordenado' string ID of the Allocation to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            UPDATE allocations
            SET estado = :estado
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', 0, PDO::PARAM_INT); // Set estado to 0 for soft delete
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error soft-deleting Allocation (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting Allocation: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error soft-deleting Allocation (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting Allocation: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the Allocation.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
             throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            validar_textovacio($this->getNombre(), 'Debe especificar el nombre');

            // Validate Porcentaje
            $porcentajeOriginal = $this->getPorcentaje(); // Get the value currently set on the object
            if ($porcentajeOriginal === null) {
                 // If the value is strictly null, it's missing.
                 throw new Exception('Debe especificar el porcentaje');
            }

            // If not null, proceed to clean and validate numeric format
            $porcentajeLimpio = format_numberclean((string)$porcentajeOriginal);
            if (!is_numeric($porcentajeLimpio)) {
                 // If after cleaning it's not numeric, it's invalid.
                 throw new Exception("El porcentaje proporcionado no es numérico después de la limpieza.");
            }
            // Re-set the value on the object with the cleaned float value
            $this->setPorcentaje((float)$porcentajeLimpio);

            // Validate porcentaje range (0-100)
            if ($this->getPorcentaje() < 0 || $this->getPorcentaje() > 100) {
                throw new Exception("El porcentaje debe estar entre 0 y 100.");
            }

            // Validate estado if it's set (allow null initially, but maybe enforce 0 or 1?)
            if ($this->getEstado() !== null && !in_array($this->getEstado(), [0, 1], true)) {
                 throw new Exception("El estado de la asignación no es válido (debe ser 0 o 1).");
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    /**
     * Resets ONLY the 'valor_bolsillo_added' (Agregar) field to 0 for ALL allocation items.
     * CRITICAL: Does NOT modify 'valor_bolsillo' or any other fields.
     *
     * @param PDO $conexion The PDO database connection.
     * @return bool True on success, false on failure.
     */
    public static function resetAllValorBolsilloAdded(PDO $conexion): bool
    {
        try {
            $query = "UPDATE allocations_items SET valor_bolsillo_added = 0";
            $statement = $conexion->prepare($query);
            return $statement->execute();
        } catch (PDOException $e) {
            error_log("Database error resetting valor_bolsillo_added for all AllocationItems: " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log("Error resetting valor_bolsillo_added for all AllocationItems: " . $e->getMessage());
            return false;
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getPorcentaje(): ?float
    {
        return $this->porcentaje;
    }

    public function setPorcentaje(?float $porcentaje): self
    {
        $this->porcentaje = $porcentaje;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        // Optional: Add validation here too, e.g., ensure $estado is 0 or 1
        // if ($estado !== null && !in_array($estado, [0, 1])) {
        //     throw new InvalidArgumentException("Estado must be 0, 1, or null.");
        // }
        $this->estado = $estado;
        return $this;
    }
}
